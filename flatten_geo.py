


import json
import logging
from math import log
import mysql.connector
from mysql.connector import Error
import warnings

warnings.filterwarnings('ignore')



# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('flatten_geo.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FlattenGeo:
    def __init__(self):
        pass

    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None

    def get_geo_ward_data(self):
        """Lấy dữ liệu brand_office theo batch, loại trừ IDs đã xử lý"""
        base_query = """
        SELECT * from geo_ward where id=726
        """

        query = base_query

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()

            logger.info(f"📊 Lấy được {len(results)} records geo_ward")
            return results

        except Error as e:
            logger.error(f"❌ Lỗi lấy dữ liệu geo_ward: {e}")
            return []


    def flatten_geo(self):
        self.connection = self.get_database_connection()
        geo_ward_data = self.get_geo_ward_data()
        for geo_ward in geo_ward_data:
            if geo_ward['geometry'] is not None:
                geo = self.flatten_json_array(json.loads(geo_ward['geometry']))
                if geo is not None:
                    json.dumps(geo)
                   

    def update_geo_ward(self, id, geo):
        query = """
        UPDATE geo_ward SET geometry = %s WHERE id = %s
        """
        self.connection.cursor().execute(query, (geo, id))
        self.connection.commit()
   
    def flatten_json_array(self,geometry):
        try:
            arr = geometry['geometry']['coordinates'][0][0]
            for index,item in enumerate(arr):
                logger.info(f"🔍 Đang xử lý item: {index} - {len(arr)}")
            
        except json.JSONDecodeError:
            return "Invalid JSON string"
            
if __name__ == "__main__":
    flatten_geo = FlattenGeo()
    flatten_geo.flatten_geo()