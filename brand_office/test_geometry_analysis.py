#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để phân tích và kiểm tra vấn đề với geometry phức tạp
"""

import json
import logging
import mysql.connector
from mysql.connector import Error
from shapely.geometry import Point, shape, Polygon, MultiPolygon
import geopandas as gpd
import time

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GeometryAnalyzer:
    def __init__(self):
        self.connection = None
        
    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def parse_geometry_safe(self, geometry_str):
        """Parse geometry từ JSON string với error handling tốt hơn"""
        try:
            if isinstance(geometry_str, str):
                geometry_data = json.loads(geometry_str)
            else:
                geometry_data = geometry_str
            
            # Lấy geometry object từ feature
            if 'geometry' in geometry_data:
                geom_obj = geometry_data['geometry']
            else:
                geom_obj = geometry_data
            
            # Tạo Shapely object
            shapely_geom = shape(geom_obj)
            
            # Validate geometry
            if not shapely_geom.is_valid:
                logger.warning(f"⚠️ Invalid geometry detected, attempting to fix...")
                shapely_geom = shapely_geom.buffer(0)  # Fix invalid geometry
                
            return shapely_geom, geom_obj.get('type', 'Unknown')
            
        except Exception as e:
            logger.error(f"❌ Lỗi parse geometry: {e}")
            return None, None
    
    def test_point_in_polygon_methods(self, geometry, point, ward_info):
        """Test các phương pháp point-in-polygon khác nhau"""
        results = {}
        
        try:
            # Method 1: contains() - chính xác nhất
            start_time = time.time()
            contains_result = geometry.contains(point)
            contains_time = time.time() - start_time
            results['contains'] = {'result': contains_result, 'time': contains_time}
            
            # Method 2: intersects() - ít chính xác hơn nhưng bao gồm boundary
            start_time = time.time()
            intersects_result = geometry.intersects(point)
            intersects_time = time.time() - start_time
            results['intersects'] = {'result': intersects_result, 'time': intersects_time}
            
            # Method 3: within() - ngược lại của contains
            start_time = time.time()
            within_result = point.within(geometry)
            within_time = time.time() - start_time
            results['within'] = {'result': within_result, 'time': within_time}
            
            # Method 4: buffer() - cho tolerance
            start_time = time.time()
            buffer_result = geometry.buffer(0.001).contains(point)
            buffer_time = time.time() - start_time
            results['buffer'] = {'result': buffer_result, 'time': buffer_time}
            
            # Method 5: distance() - kiểm tra khoảng cách
            start_time = time.time()
            distance = geometry.distance(point)
            distance_time = time.time() - start_time
            results['distance'] = {'result': distance, 'time': distance_time}
            
        except Exception as e:
            logger.error(f"❌ Lỗi test point-in-polygon: {e}")
            
        return results
    
    def analyze_complex_geometries(self):
        """Phân tích các geometry phức tạp"""
        self.connection = self.get_database_connection()
        if not self.connection:
            return
            
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Lấy các geometry phức tạp nhất
            query = """
            SELECT id, geo_province_code, province_title, ward_title, geometry,
                   KINH_DO as longitude, VI_DO as latitude
            FROM geo_ward 
            WHERE geometry IS NOT NULL 
            AND KINH_DO IS NOT NULL AND VI_DO IS NOT NULL
            ORDER BY CHAR_LENGTH(geometry) DESC 
            LIMIT 5
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            logger.info(f"🔍 Phân tích {len(results)} geometry phức tạp nhất...")
            
            for i, row in enumerate(results):
                logger.info(f"\n=== GEOMETRY {i+1}: {row['ward_title']}, {row['province_title']} ===")
                logger.info(f"ID: {row['id']}, Size: {len(row['geometry'])} chars")
                
                # Parse geometry
                geometry, geom_type = self.parse_geometry_safe(row['geometry'])
                if not geometry:
                    logger.error("❌ Không thể parse geometry")
                    continue
                
                logger.info(f"Geometry Type: {geom_type}")
                logger.info(f"Is Valid: {geometry.is_valid}")
                logger.info(f"Area: {geometry.area:.6f}")
                logger.info(f"Bounds: {geometry.bounds}")
                
                # Phân tích cấu trúc MultiPolygon
                if isinstance(geometry, MultiPolygon):
                    logger.info(f"MultiPolygon với {len(geometry.geoms)} polygon(s)")
                    for j, poly in enumerate(geometry.geoms):
                        if isinstance(poly, Polygon):
                            exterior_points = len(poly.exterior.coords)
                            interior_rings = len(poly.interiors)
                            logger.info(f"  Polygon {j+1}: {exterior_points} exterior points, {interior_rings} holes")
                elif isinstance(geometry, Polygon):
                    exterior_points = len(geometry.exterior.coords)
                    interior_rings = len(geometry.interiors)
                    logger.info(f"Polygon: {exterior_points} exterior points, {interior_rings} holes")
                
                # Test point-in-polygon với tọa độ trung tâm của ward
                try:
                    center_lng = float(row['longitude'])
                    center_lat = float(row['latitude'])
                    test_point = Point(center_lng, center_lat)
                    
                    logger.info(f"Testing point ({center_lat}, {center_lng})...")
                    test_results = self.test_point_in_polygon_methods(geometry, test_point, row)
                    
                    for method, result in test_results.items():
                        if method == 'distance':
                            logger.info(f"  {method}: {result['result']:.6f} (time: {result['time']:.4f}s)")
                        else:
                            logger.info(f"  {method}: {result['result']} (time: {result['time']:.4f}s)")
                            
                except Exception as e:
                    logger.error(f"❌ Lỗi test point: {e}")
                
                logger.info("-" * 50)
            
            cursor.close()
            
        except Exception as e:
            logger.error(f"❌ Lỗi phân tích: {e}")
        finally:
            if self.connection:
                self.connection.close()
    
    def test_sample_coordinates(self):
        """Test với một số tọa độ mẫu"""
        test_points = [
            # Hà Nội
            (21.0285, 105.8542, "Hà Nội center"),
            # TP.HCM  
            (10.8231, 106.6297, "TP.HCM center"),
            # Đà Nẵng
            (16.0544, 108.2022, "Đà Nẵng center"),
            # Một điểm ở biển (không thuộc ward nào)
            (15.0, 110.0, "Biển Đông"),
        ]
        
        self.connection = self.get_database_connection()
        if not self.connection:
            return
            
        try:
            # Lấy tất cả geometry
            cursor = self.connection.cursor(dictionary=True)
            query = """
            SELECT id, geo_province_code, province_title, ward_title, geometry
            FROM geo_ward 
            WHERE geometry IS NOT NULL 
            LIMIT 100
            """
            cursor.execute(query)
            ward_data = cursor.fetchall()
            cursor.close()
            
            logger.info(f"🔍 Test {len(test_points)} điểm với {len(ward_data)} wards...")
            
            for lat, lng, description in test_points:
                logger.info(f"\n=== TEST POINT: {description} ({lat}, {lng}) ===")
                test_point = Point(lng, lat)
                found_wards = []
                
                for ward in ward_data:
                    geometry, geom_type = self.parse_geometry_safe(ward['geometry'])
                    if geometry and geometry.contains(test_point):
                        found_wards.append({
                            'id': ward['id'],
                            'ward': ward['ward_title'],
                            'province': ward['province_title'],
                            'geom_type': geom_type
                        })
                
                if found_wards:
                    logger.info(f"✅ Tìm thấy {len(found_wards)} ward(s):")
                    for ward in found_wards:
                        logger.info(f"  - {ward['ward']}, {ward['province']} (ID: {ward['id']}, Type: {ward['geom_type']})")
                else:
                    logger.info("❌ Không tìm thấy ward nào")
                    
        except Exception as e:
            logger.error(f"❌ Lỗi test coordinates: {e}")
        finally:
            if self.connection:
                self.connection.close()

if __name__ == "__main__":
    analyzer = GeometryAnalyzer()
    
    print("🚀 BẮT ĐẦU PHÂN TÍCH GEOMETRY...")
    print("=" * 60)
    
    # Test 1: Phân tích geometry phức tạp
    print("\n1. PHÂN TÍCH GEOMETRY PHỨC TẠP")
    analyzer.analyze_complex_geometries()
    
    # Test 2: Test với tọa độ mẫu
    print("\n2. TEST VỚI TỌA ĐỘ MẪU")
    analyzer.test_sample_coordinates()
    
    print("\n🎉 HOÀN THÀNH PHÂN TÍCH!")
