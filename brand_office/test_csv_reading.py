#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra function get_processed_ids đã được cải thiện
"""

import sys
import os
import logging

# Thêm thư mục hiện tại vào Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from update_brand_office_address import BrandOfficeAddressUpdater

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_get_processed_ids():
    """Test function get_processed_ids với file có vấn đề"""
    
    logger.info("🧪 TESTING get_processed_ids function")
    logger.info("=" * 50)
    
    try:
        # Tạo instance của BrandOfficeAddressUpdater
        updater = BrandOfficeAddressUpdater()
        
        # Test với file brand_offices_updated.csv
        filename = "brand_offices_updated.csv"
        logger.info(f"📄 Testing với file: {filename}")
        
        # Gọi function get_processed_ids
        processed_ids = updater.get_processed_ids(filename)
        
        logger.info(f"✅ Kết quả:")
        logger.info(f"   - Số lượng IDs đọc được: {len(processed_ids)}")
        logger.info(f"   - Kiểu dữ liệu: {type(processed_ids)}")
        
        if processed_ids:
            # Hiển thị một vài ID đầu tiên
            sample_ids = list(processed_ids)[:10]
            logger.info(f"   - Mẫu IDs: {sample_ids}")
            
            # Kiểm tra ID cuối cùng (gần dòng 33117)
            if '196723' in processed_ids:
                logger.info(f"   - ✅ ID 196723 (dòng 33117) đã được đọc thành công")
            else:
                logger.warning(f"   - ⚠️ ID 196723 (dòng 33117) không được tìm thấy")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Lỗi trong test: {e}")
        return False

def main():
    """Hàm chính"""
    logger.info("🚀 Bắt đầu test CSV reading")
    
    success = test_get_processed_ids()
    
    if success:
        logger.info("✅ Test hoàn thành thành công!")
    else:
        logger.error("❌ Test thất bại!")
        
    return success

if __name__ == "__main__":
    main()
